{"info": {"_postman_id": "5439c0d1-a67c-4fa6-80fe-775dd6f94bf6", "name": "Smoke Testing Copy", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "5675932"}, "item": [{"name": "New ConversationID", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["//postman.setGlobalVariable(\"var1\", null);", "var jbody = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"conversationID\", jbody.c.id);", "tests[\"Conversation ID: \" + jbody.c.id] = responseBody.has(\"id\");", "", "//tests[\"VetHub is down test\" + responseBody] = responseBody === \"No HTTP resource was found that matches the request URI 'http://api-sit.vethub.net/api/Claim/NewConversationId'.\";", ""]}}], "request": {"method": "GET", "header": [{"key": "TransactionId", "value": "EzyVetClaim{{date}}3"}, {"key": "EZYVET", "value": "", "disabled": true}, {"key": "RecipientId", "value": "B37CF562-E88C-4908-9D31-BF447FFB3026", "disabled": true}, {"key": "VendorPassword", "value": "D0C7100F-DDD7-4D95-8FE2-11BD4C472D95"}, {"key": "UserID", "value": "{{userIDEzyVet}}"}, {"key": "UserPassword", "value": "{{userPasswordEzyVet}}"}, {"key": "VendorSystem", "value": "EzyVet", "disabled": true}, {"key": "VendorVersion", "value": "1.0", "disabled": true}, {"key": "FormatType", "value": "JSON"}, {"key": "OPENVPMS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "OpenVPMS{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DEE79294-0FAA-43DE-B236-F173D4A87E28", "disabled": true}, {"key": "UserPassword", "value": "55cffbbe", "disabled": true}, {"key": "FormatType", "value": "JSON", "disabled": true}, {"key": "RXWORKS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "EzyVet{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DB18590E-A528-4CFC-8AA9-584E8FE99924", "disabled": true}, {"key": "UserID", "value": "CHATSWOOD VET", "disabled": true}, {"key": "UserPassword", "value": "TESTPASSWORD", "disabled": true}, {"key": "FormatType", "value": "XML", "disabled": true}], "url": {"raw": "{{vethubURL}}/api/Claim/NewConversationId", "host": ["{{vethubURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "NewConversationId"]}, "description": "SIT, EzyVet, JSON"}, "response": []}, {"name": "New AttachmentID", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jbody = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"attachmentID\", jbody.c.id);", "tests[\"Attachment ID: \" + jbody.c.id] = responseBody.has(\"id\");"]}}], "request": {"method": "GET", "header": [{"key": "TransactionId", "value": "EzyVetClaim{{date}}3"}, {"key": "EZYVET", "value": "", "disabled": true}, {"key": "RecipientId", "value": "B37CF562-E88C-4908-9D31-BF447FFB3026", "disabled": true}, {"key": "VendorPassword", "value": "D0C7100F-DDD7-4D95-8FE2-11BD4C472D95"}, {"key": "UserID", "value": "{{userIDEzyVet}}"}, {"key": "UserPassword", "value": "{{userPasswordEzyVet}}"}, {"key": "VendorSystem", "value": "EzyVet", "disabled": true}, {"key": "VendorVersion", "value": "1.0", "disabled": true}, {"key": "FormatType", "value": "JSON"}, {"key": "OPENVPMS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "OpenVPMS{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DEE79294-0FAA-43DE-B236-F173D4A87E28", "disabled": true}, {"key": "UserID", "value": "openvpmssit1", "disabled": true}, {"key": "UserPassword", "value": "55cffbbe", "disabled": true}, {"key": "FormatType", "value": "JSON", "disabled": true}, {"key": "RXWORKS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "RxWorks{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DB18590E-A528-4CFC-8AA9-584E8FE99924", "disabled": true}, {"key": "UserID", "value": "CHATSWOOD VET", "disabled": true}, {"key": "UserPassword", "value": "TESTPASSWORD", "disabled": true}, {"key": "FormatType", "value": "XML", "disabled": true}], "url": {"raw": "{{vethubURL}}/api/Claim/{{conversationID}}/Attachment/NewConversationId", "host": ["{{vethubURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{conversationID}}", "Attachment", "NewConversationId"]}, "description": "SIT, EzyVet, JSON"}, "response": []}, {"name": "Submit Attachment clinical", "request": {"method": "POST", "header": [{"key": "FormatType", "value": "JSON"}, {"key": "Content-Type", "value": "image/jpg"}, {"key": "DocumentName", "value": "laqp1.JPG"}, {"key": "TransactionId", "value": "EzyVetClaim{{date}}a"}, {"key": "EZYVET", "value": "", "disabled": true}, {"key": "RecipientId", "value": "B37CF562-E88C-4908-9D31-BF447FFB3026", "disabled": true}, {"key": "VendorPassword", "value": "D0C7100F-DDD7-4D95-8FE2-11BD4C472D95"}, {"key": "UserID", "value": "{{userIDEzyVet}}"}, {"key": "UserPassword", "value": "{{userPasswordEzyVet}}"}, {"key": "VendorSystem", "value": "EzyVet", "disabled": true}, {"key": "VendorVersion", "value": "1.0", "disabled": true}, {"key": "FormatType", "value": "JSON", "disabled": true}, {"key": "OPENVPMS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "OpenVPMS{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DEE79294-0FAA-43DE-B236-F173D4A87E28", "disabled": true}, {"key": "UserID", "value": "openvpmssit1", "disabled": true}, {"key": "UserPassword", "value": "55cffbbe", "disabled": true}, {"key": "FormatType", "value": "JSON", "disabled": true}, {"key": "RXWORKS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "RxWorks{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DB18590E-A528-4CFC-8AA9-584E8FE99924", "disabled": true}, {"key": "UserID", "value": "CHATSWOOD VET", "disabled": true}, {"key": "UserPassword", "value": "TESTPASSWORD", "disabled": true}, {"key": "FormatType", "value": "XML", "disabled": true}, {"key": "ClientId", "value": "VETHUB.API"}, {"key": "APIKey", "value": "4c7f08ca-11fd-4250-8ec8-e5abeabcb906"}], "body": {"mode": "file", "file": {}}, "url": {"raw": "{{vethubURL}}/api/Claim/{{conversationID}}/Attachment/{{attachmentID}}", "host": ["{{vethubURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{conversationID}}", "Attachment", "{{attachmentID}}"]}, "description": "SIT, EzyVet, JSON"}, "response": []}, {"name": "Submit <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "TransactionId", "value": "EzyVetClaim{{date}}3"}, {"key": "EZYVET", "value": "", "disabled": true}, {"key": "RecipientId", "value": "B37CF562-E88C-4908-9D31-BF447FFB3026", "disabled": true}, {"key": "VendorPassword", "value": "D0C7100F-DDD7-4D95-8FE2-11BD4C472D95"}, {"key": "UserID", "value": "{{userIDEzyVet}}"}, {"key": "UserPassword", "value": "{{userPasswordEzyVet}}"}, {"key": "VendorSystem", "value": "EzyVet", "disabled": true}, {"key": "VendorVersion", "value": "1.0", "disabled": true}, {"key": "FormatType", "value": "JSON"}, {"key": "OPENVPMS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "OpenVPMS{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DEE79294-0FAA-43DE-B236-F173D4A87E28", "disabled": true}, {"key": "UserID", "value": "openvpmssit1", "disabled": true}, {"key": "UserPassword", "value": "55cffbbe", "disabled": true}, {"key": "FormatType", "value": "JSON", "disabled": true}, {"key": "RXWORKS", "value": "", "disabled": true}, {"key": "TransactionId", "value": "RxWorks{{date}}a", "disabled": true}, {"key": "VendorPassword", "value": "DB18590E-A528-4CFC-8AA9-584E8FE99924", "disabled": true}, {"key": "UserID", "value": "CHATSWOOD VET", "disabled": true}, {"key": "UserPassword", "value": "TESTPASSWORD", "disabled": true}, {"key": "FormatType", "value": "XML", "disabled": true}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"Identification\": {\r\n    \"OwnerID\": \"45652\",\r\n    \"PracticeID\": \"petsure\",\r\n    \"PracticeClaimRef\": \"1545\",\r\n    \"RequestGap\": \"Y\"\r\n  },\r\n  \"InfoFromPolicyHolder\": {\r\n    \"PolicyDetails\": {\r\n      \"PolicyNumber\": \"WW00003731S\",\r\n      \"PolicyholderName\": \"Stella Seater\",\r\n      \"Address\": \"5 Laurie Court, SKYE, VIC\",\r\n      \"Postcode\": \"3977\",\r\n      \"EmailAddress\": \"<EMAIL>\",\r\n      \"PreferredContactBy\": \"Email\",\r\n      \"InsuranceCompany\": \"JSON Compnay\",\r\n      \"ExpiryDate\": \"2020-09-23\"\r\n    },\r\n    \"AnimalDetails\": {\r\n      \"Name\": \"<PERSON>\",\r\n      \"Species\": \"Canine\",\r\n      \"Breed\": \"Rottweiler\",\r\n      \"DateOfBirth\": \"2014-09-23\",\r\n      \"Gender\": \"Female\",\r\n      \"Colour\": \"Black & Tan\",\r\n      \"MicrochipNumber\": \"\"\r\n    },\r\n    \"Conditions\": [ {\r\n        \"Description\": \"Torn Nail (different nail from last time)\",\r\n        \"DateFirstNoticed\": \"2016-12-03\"\r\n      },\r\n      {\r\n        \"Description\": \"Snake Bite\",\r\n        \"DateFirstNoticed\": \"2017-12-03\"\r\n      }\r\n    ],\r\n    \"Financial\": {\r\n      \"PayVet\": \"true\",\r\n      \"PayClaimTo\": \"vet\"\r\n    }\r\n  },\r\n  \"InfoFromVet\": {\r\n    \"Miscellaneous\": {\r\n      \"AnimalRegistered\": \"2015-04-04\",\r\n      \"LastVaccination\": \"2016-11-11\",\r\n      \"MicrochipNumber\": \"956000003172356\",\r\n      \"ClaimHandler\": {\r\n        \"Name\": \"Tonia Brajcich  Cert IV Veterinary Nursing\",\r\n        \"Email\": \"<EMAIL>\"\r\n      }\r\n    },\r\n    \"Vet\": {\r\n      \"VetSurname\": \"Morgan Woodforde BVMS BSc \",\r\n      \"VetForenames\": \"Dr\",\r\n      \"PracticeName\": \"REMTest\",\r\n      \"PracticeAddress\": \"REMTest\",\r\n      \"PracticePhone\": \"9277 7488\",\r\n      \"PracticePostcode\": \"2069\",\r\n      \"RegistrationNumber\": \"420\",\r\n    },\r\n    \"AnimalClinicalHistory\": [\r\n      {\r\n        \"Date\": \"2016-12-03\",\r\n        \"Time\": \"15:08 PM\",\r\n        \"EnteredBy\": \"Dr Morgan Woodforde BVMS BSc \",\r\n        \"TextEntry\": \"Reason: Appt: Torn Nail    History: Noticed has torn nail today, does not seem as bad as last time.  Metacam intermittently, no other concerns today.    Examination:   BAR lovely dog  MM pink ,moist, CRT<2, teeth excellent  Left front 4th digit broken at end of quick  HR normal no murmur, chest sounds clear    Treatment: trimmed nail in tx, no bleeding from base    Assessment:   1. Left fore 4th nail injury    Plan:   1. Metacam as required for joints  2. If nail becomes infected can dispense antibiotics, not required at this stage      Vital Signs        Weight: 34.2;         \"\r\n      }\r\n    ],\r\n    \"Conditions\": [\r\n      {\r\n        \"DiagnosisOrSigns\": \"Torn Nail (different nail from last time)123\",\r\n        \"ClaimContinuation\": \"0\",\r\n    \"ConditionCode\": \"Code1223\",\r\n        \"Started\": \"2016-12-03\",\r\n        \"DeathOrEuthanasia\": \"false\",\r\n        \"OngoingCondition\": \"false\",\r\n        \"TreatmentDates\": {\r\n          \"DateFrom\": \"2016-12-03\",\r\n          \"DateTo\": \"2016-12-03\"\r\n        },\r\n        \"Financial\": {\r\n          \"TotalExVAT\": \"214.7727\",\r\n          \"VAT\": \"11.4773\",\r\n          \"TotalIncVat\": \"126.2700\",\r\n          \"TotalDiscount\": \"57.59\",\r\n          \"InvoiceItems\": [\r\n            {\r\n              \"Description\": \"Sick Patient Revisit Short\",\r\n              \"Type\": \"Examinations\",\r\n              \"AmountExVAT\": \"29.0909\",\r\n              \"InvoiceNumber\": \"768677\",\r\n              \"Date\": \"2016-12-03\"\r\n            },\r\n            {\r\n              \"Description\": \"Metacam 100mls Liquid\",\r\n              \"Type\": \"Medications-Oral\",\r\n              \"AmountExVAT\": \"85.6818\",\r\n              \"InvoiceNumber\": \"768677\",\r\n              \"Date\": \"2016-12-03\"\r\n            }\r\n          ],\r\n          \"Invoices\": [\r\n            {\r\n              \"Invoice\": {\r\n                \"InvoiceNumber\": \"test_invoicenumber\",\r\n                \"Date\": \"2015-12-03\",\r\n                \"TotalExVAT\": \"30.02\",\r\n                \"TotalDiscountExVAT\": \"125.58\",\r\n                \"VAT\": \"12.23\",\r\n                \"TotalIncVat\": \"158\",\r\n                \"Items\": [\r\n                  {\r\n                    \"Item\": {\r\n                      \"ItemCode\": \"test_itemcode\",\r\n                      \"Sequence\": \"test_seq1\",\r\n                      \"Description\": \"test_desc\",\r\n                      \"Type\": \"test_type\",\r\n                      \"AmountExVAT\": \"5.59\",\r\n                      \"DiscountExVAT\": \"58.65\",\r\n                      \"VAT\": \"98.98\",\r\n                      \"Quantity\": \"45.59\",\r\n                      \"TotalIncVAT\": \"98.59\"\r\n                    }\r\n                  }\r\n                ]\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        \"Remarks\": \"I hereby agree that:    1.     All material submitted in connection with this claim is true, accurate and complete in all material respects and no relevant information has been withheld, including without limitation, any discounts which may have been applied;    2.     The veterinary practice has obtained the authority of the policyholder to submit this claim on their behalf; and    3.     The veterinary practice has received payment in full from the policyholder in respect of any service or treatment connected with this claim.    4.     PetSure and its personnel can request veterinary practice  records connected with this claim for audit purposes.    Dr Morgan Woodforde BVMS BSc on 09/12/2016\"\r\n      },\r\n    {\r\n        \"DiagnosisOrSigns\": \"Snake Bite\",\r\n        \"ClaimContinuation\": \"0\",\r\n        \"ConsultationNotes\": \"This is a test\",\r\n    \"ConditionCode\": \"Code12345\",\r\n        \"Started\": \"2016-12-03\",\r\n        \"DeathOrEuthanasia\": \"false\",\r\n        \"OngoingCondition\": \"false\",\r\n        \"TreatmentDates\": {\r\n          \"DateFrom\": \"2016-12-03\",\r\n          \"DateTo\": \"2016-12-03\"\r\n        },\r\n        \"Financial\": {\r\n          \"TotalExVAT\": \"114.7727\",\r\n          \"VAT\": \"11.4773\",\r\n          \"TotalIncVat\": \"126.2500\",\r\n          \"TotalDiscount\": \"57.59\",\r\n          \"InvoiceItems\": [\r\n            {\r\n              \"Description\": \"Sick Patient Rev\",\r\n              \"Type\": \"Examinations\",\r\n              \"AmountExVAT\": \"29.0909\",\r\n              \"InvoiceNumber\": \"768677\",\r\n              \"Date\": \"2016-12-03\"\r\n            },\r\n            {\r\n              \"Description\": \"Metacam 100m\",\r\n              \"Type\": \"Medications-Oral\",\r\n              \"AmountExVAT\": \"85.6818\",\r\n              \"InvoiceNumber\": \"768677\",\r\n              \"Date\": \"2016-12-03\"\r\n            }\r\n          ],\r\n          \"Invoices\": [\r\n            {\r\n              \"Invoice\": {\r\n                \"InvoiceNumber\": \"test_invoicenumbe23\",\r\n                \"Date\": \"2015-12-03\",\r\n                \"TotalExVAT\": \"30.02\",\r\n                \"TotalDiscountExVAT\": \"125.58\",\r\n                \"VAT\": \"12.23\",\r\n                \"TotalIncVat\": \"158\",\r\n                \"Items\": [\r\n                  {\r\n                    \"Item\": {\r\n                      \"ItemCode\": \"test_itemco34\",\r\n                      \"Sequence\": \"test_seq1\",\r\n                      \"Description\": \"test_desc\",\r\n                      \"Type\": \"test_type\",\r\n                      \"AmountExVAT\": \"5.59\",\r\n                      \"DiscountExVAT\": \"58.65\",\r\n                      \"VAT\": \"98.98\",\r\n                      \"Quantity\": \"45.59\",\r\n                      \"TotalIncVAT\": \"98.59\"\r\n                    }\r\n                  }\r\n                ]\r\n              },\r\n              \"ServiceProviderNumber\": \"CT0000040\"\r\n            }\r\n          ]\r\n        },\r\n        \"Remarks\": \"I hereby agree that:    1.     All material submitted in connection with this claim is true, accurate and complete in all material respects and no relevant information has been withheld, including without limitation, any discounts which may have been applied;    2.     The veterinary practice has obtained the authority of the policyholder to submit this claim on their behalf; and    3.     The veterinary practice has received payment in full from the policyholder in respect of any service or treatment connected with this claim.    4.     PetSure and its personnel can request veterinary practice  records connected with this claim for audit purposes.    Dr Morgan Woodforde BVMS BSc on 09/12/2016\"\r\n      }\r\n    ],\r\n    \"Referrals\": [\r\n      {\r\n        \"ReferralType\": \"General\",\r\n        \"ReferralReason\": \"Left hind lameness / Hip dysplasia\",\r\n        \"ReferralPractice\": {\r\n          \"Name\": \"Rivergum Referral Centre\",\r\n          \"Address\": \"PO Box 344, Willeton, WA, 6155\",\r\n          \"Phone\": \"08 9259-6344\"\r\n        },\r\n        \"ReferralDate\": \"2015-07-26\"\r\n      }\r\n    ]\r\n  }\r\n}"}, "url": {"raw": "{{vethubURL}}/api/Claim/{{conversationID}}", "host": ["{{vethubURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{conversationID}}"]}, "description": "SIT, EzyVet, JSON"}, "response": []}, {"name": "cosSubmitClaim-OK Copy", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jbody = JSON.parse(responseBody);", "", "pm.test(\"Response is 200 OK\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Claim Reference Number is generated\", function () {", "    pm.expect(jbody).to.not.be.null;", "    pm.expect(jbody).to.include('C' + environment.env);", "});", "", "postman.setEnvironmentVariable(\"claimRefNumber\", jbody);", "", "setTimeout(function(){}, 10000);"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authToken}}"}, {"key": "ApplicationName", "value": "CSP"}, {"key": "TransactionID", "value": "{{date}}"}, {"key": "APIKey", "value": "{{APIKey-COS}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ClientId", "value": "{{clientID-COS}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"Header\": {\r\n    \"Source\": \"CSP\",\r\n    \"RequestType\": \"Claim\",\r\n    \"ClaimType\": \"Routine\",\r\n    \"ClaimRefNumber\": null,\r\n    \"BrandPartner\": \"WW\"\r\n  },\r\n  \"ClaimingVet\": {\r\n    \"VetPracticeId\": \"CT0000013\",\r\n    \"VetPracticeName\": \"A V Medenis & Associates\",\r\n    \"TreatingVetName\": \"<PERSON> <PERSON><PERSON>\",\r\n    \"LodgedBy\": \"Dr Shu<PERSON>\"\r\n  },\r\n  \"Customer\": {\r\n    \"Name\": \"Harvey SpecterHarvey Specter\",\r\n    \"Address\": \"23 Pentacostal Ave, Waverley\",\r\n    \"PostCode\": \"2027\"\r\n  },\r\n  \"Pet\": {\r\n    \"Name\": \"<PERSON>\",\r\n    \"DOB\": \"2013-03-21\",\r\n    \"Gender\": \"Male\",\r\n    \"Species\": \"Canine\",\r\n    \"Breed\": \"Miniature Dachshund - Smooth Haired\",\r\n    \"Pedigree\": null,\r\n    \"Colour\": \"Dark Brown\",\r\n    \"MicrochipNumber\": \"2879357298\"\r\n  },\r\n  \"Policy\": {\r\n    \"PolicyNumber\": \"0003074\",\r\n    \"PolicyExpiryDate\": \"2018-04-10\",\r\n    \"FullPolicyNumber\": \"WW00030741\"\r\n  },\r\n  \"TreatmentDetailsFromPolicyHolder\": {\r\n    \"Conditions\": [\r\n      {\r\n        \"Description\": \"Broken Leg\",\r\n        \"FirstNoticed\": \"2016-06-23\",\r\n        \"TreatedFrom\": \"2016-06-23\",\r\n        \"TreatedTo\": \"2016-11-19\"\r\n      }\r\n    ],\r\n    \"Vets\": [\r\n      {\r\n        \"Name\": \"John Fiodregder\",\r\n        \"PracticeName\": \"Dog Street Centre\",\r\n        \"Address\": \"23 Waterbridge Rd, Randwick\",\r\n        \"PostCode\": \"2013\",\r\n        \"ContactNumber\": \"02 9635 1734\",\r\n        \"Email\": null,\r\n        \"DateFrom\": \"2016-06-24\",\r\n        \"DateTo\": \"2016-07-01\",\r\n        \"IsPrimary\": 1\r\n      }\r\n    ]\r\n  },\r\n  \"TotalDollarValue\": 150.10,\r\n  \"Description\": \"Test data for claims\",\r\n  \"Documents\": [\r\n    {\r\n      \"DocumentPath\": \"cosservice-sit-claims-20190603/e554283f-3848-4647-9c1e-8b62da10ae7d.pdf\",\r\n      \"DocumentId\": \"B524CC82-A9E3-47B2-AC8F-04DD25D00EC3\",\r\n      \"DocumentName\": \"TestFF.pdf\",\r\n      \"DocumentType\": \"pdf\",\r\n      \"DocumentDate\": \"2017-05-01T02:12:40.068282Z\"\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{cosURL}}/api/Claims", "host": ["{{cosURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>"]}, "description": "COSAPI: Submit <PERSON><PERSON><PERSON>"}, "response": []}, {"name": "submitAdditionalAttachments-COMM Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response code is 200 OK\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "setTimeout(function(){}, 10000);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "ApplicationName", "value": "COS"}, {"key": "TransactionID", "value": "{{date}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "APIKey", "value": "{{APIKey-COS}}"}, {"key": "ClientId", "value": "{{clientID-COS}}"}, {"key": "Authorization", "value": "{{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"ClaimNumber\": \"{{claimNumber}}\",\r\n  \"PolicyNumber\": \"{{policyNumber}}\",\r\n  \"ClaimRefNumber\": \"{{claimRefNumber}}\",\r\n  \"Source\": \"{{source}}\",\r\n  \"Comments\": \"{{oldDocumentID}} Automation Testing for additional attachment\",\r\n  \"DocumentTypeList\": []\r\n}\r\n"}, "url": {"raw": "{{cosURL}}/api/Claims/Attachments", "host": ["{{cosURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "Attachments"]}, "description": "COSAPI: Submit Attachments requires ClaimRefNumber from previous Submit Claim"}, "response": []}, {"name": "submitAdditionalAttachments-INVOICE Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response code is 200 OK\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "setTimeout(function(){}, 10000);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "ApplicationName", "value": "COS"}, {"key": "TransactionID", "value": "LAP030702"}, {"key": "Content-Type", "value": "application/json"}, {"key": "APIKey", "value": "{{APIKey-COS}}"}, {"key": "ClientId", "value": "{{clientID-COS}}"}, {"key": "Authorization", "value": "{{authToken}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"ClaimNumber\": \"{{claimNumber}}\",\r\n  \"PolicyNumber\": \"{{policyNumber}}1\",\r\n  \"ClaimRefNumber\": \"{{claimRefNumber}}\",\r\n  \"Source\": \"{{source}}\",\r\n  \"Comments\": \"\",\r\n  \"DocumentTypeList\": [\r\n\t\t{\r\n\t\t\"FilePath\": \"{{documentPath}}\",\r\n\t\t\"DocumentName\": \"{{oldDocumentID}}{{documentName}}\",\r\n\t\t\"DocumentCode\": \"INVOICE\"\r\n\t\t}\r\n\t]\r\n}\r\n"}, "url": {"raw": "{{cosURL}}/api/Claims/Attachments", "host": ["{{cosURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "Attachments"]}, "description": "COSAPI: Submit Attachments requires ClaimRefNumber from previous Submit Claim"}, "response": []}, {"name": "submitAdditionalAttachments-INVOICE Copy Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response code is 200 OK\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "setTimeout(function(){}, 10000);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "ApplicationName", "value": "COS"}, {"key": "TransactionID", "value": "LAP030702"}, {"key": "Content-Type", "value": "application/json"}, {"key": "APIKey", "value": "{{APIKey-COS}}"}, {"key": "ClientId", "value": "{{clientID-COS}}"}, {"key": "Authorization", "value": "{{authToken}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"ClaimNumber\": \"{{claimNumber}}\",\r\n  \"PolicyNumber\": \"{{policyNumber}}1\",\r\n  \"ClaimRefNumber\": \"{{claimRefNumber}}\",\r\n  \"Source\": \"{{source}}\",\r\n  \"Comments\": \"\",\r\n  \"DocumentTypeList\": [\r\n\t\t{\r\n\t\t\"FilePath\": \"{{documentPath}}\",\r\n\t\t\"DocumentName\": \"{{oldDocumentID}}{{documentName}}\",\r\n\t\t\"DocumentCode\": \"INVOICE\"\r\n\t\t}\r\n\t]\r\n}\r\n"}, "url": {"raw": "{{cosURL}}/api/Claims/Attachments", "host": ["{{cosURL}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "Attachments"]}, "description": "COSAPI: Submit Attachments requires ClaimRefNumber from previous Submit Claim"}, "response": []}]}