import json
import requests
import os

def run_postman_requests(collection_path, environment_path):
    """
    Runs a series of requests defined in a Postman collection,
    using environment variables.

    Args:
        collection_path (str): The file path for the Postman collection JSON.
        environment_path (str): The file path for the Postman environment JSON.
    """
    try:
        with open(environment_path, 'r') as f:
            env_data = json.load(f)
        
        env_vars = {item['key']: item['value'] for item in env_data.get('values', [])}
        base_url = env_vars.get('vethubURL')

        if not base_url:
            print("❌ 'vethubURL' not found in the environment file.")
            return

        with open(collection_path, 'r') as f:
            collection_data = json.load(f)

        # --- 1. Get ConversationID ---
        print("🚀 Starting the process...")
        print("\n--- Step 1: Getting Conversation ID ---")
        try:
            new_conversation_url = f"{base_url}/api/claim/NewConversationId"
            response_conv = requests.get(new_conversation_url)
            response_conv.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
            conversation_id = response_conv.json().get('ConversationId')
            if conversation_id:
                print(f"✅ Successfully retrieved ConversationID: {conversation_id}")
            else:
                print("❌ Could not find 'ConversationId' in the response.")
                return
        except requests.exceptions.RequestException as e:
            print(f"Error during Get ConversationID request: {e}")
            return

        # --- 2. Get AttachmentID ---
        print("\n--- Step 2: Getting Attachment ID ---")
        try:
            new_attachment_url = f"{base_url}/api/claim/{conversation_id}/Attachment/NewConversationId"
            response_attach_id = requests.get(new_attachment_url)
            response_attach_id.raise_for_status()
            attachment_id = response_attach_id.json().get('AttachmentId') # Assuming the key is 'AttachmentId'
            if attachment_id:
                 print(f"✅ Successfully retrieved AttachmentID: {attachment_id}")
            else:
                print("❌ Could not find 'AttachmentId' in the response.")
                return
        except requests.exceptions.RequestException as e:
            print(f"Error during Get AttachmentID request: {e}")
            return


        # --- 3. Submit Attachment (Clinical) ---
        print("\n--- Step 3: Submitting Attachment ---")
        # You will need to specify the path to your clinical attachment file
        attachment_file_path = 'path/to/your/attachment.pdf' # <--- CHANGE THIS
        
        if not os.path.exists(attachment_file_path):
            print(f"❌ Attachment file not found at: {attachment_file_path}")
            return
            
        submit_attachment_url = f"{base_url}/api/claim/attachment/{attachment_id}"
        
        try:
            with open(attachment_file_path, 'rb') as f:
                files = {'file': (os.path.basename(attachment_file_path), f)}
                response_submit_attach = requests.post(submit_attachment_url, files=files)
                response_submit_attach.raise_for_status()
            print(f"✅ Attachment submitted successfully. Status Code: {response_submit_attach.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"Error during Submit Attachment request: {e}")
            return
        except FileNotFoundError:
            print(f"❌ Attachment file not found at path: {attachment_file_path}")
            return


        # --- 4. Submit Claim ---
        print("\n--- Step 4: Submitting Claim ---")
        submit_claim_url = f"{base_url}/api/claim/{conversation_id}"
        
        # You need to provide the JSON body for the claim submission
        # This is an example, modify it to match your actual claim data
        claim_data = {
            "claimDetails": "your_claim_details",
            "amount": "100.00"
            # Add other necessary fields for your claim
        }

        try:
            response_submit_claim = requests.post(submit_claim_url, json=claim_data)
            response_submit_claim.raise_for_status()
            print(f"✅ Claim submitted successfully! Status Code: {response_submit_claim.status_code}")
            print("Response Body:")
            print(response_submit_claim.json())
        except requests.exceptions.RequestException as e:
            print(f"Error during Submit Claim request: {e}")
            return
            
        print("\n🎉 All steps completed successfully!")


    except FileNotFoundError as e:
        print(f"❌ Error: {e}. Please make sure the file paths are correct.")
    except json.JSONDecodeError:
        print("❌ Error decoding JSON. Please check the format of your collection and environment files.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    # --- Configuration ---
    # Update these paths if your files are in a different directory
    collection_filename = 'collection.json'
    environment_filename = 'environment.json'

    run_postman_requests(collection_filename, environment_filename)